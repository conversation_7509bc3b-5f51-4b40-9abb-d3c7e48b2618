import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  Dimensions,
} from 'react-native';
import { MapPin, Heart, X } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Animated from 'react-native-reanimated';

const { width: screenWidth } = Dimensions.get('window');

interface Pet {
  id: string;
  name: string;
  breed: string;
  age: string;
  price: number;
  image: string;
  type: 'dog' | 'cat';
  location: string;
  seller: string;
}

interface SwipeCardProps {
  pet: Pet;
  style?: any;
}

export default function SwipeCard({ pet, style }: SwipeCardProps) {
  return (
    <Animated.View style={[styles.card, style]}>
      <Image source={{ uri: pet.image }} style={styles.image} />
      
      <LinearGradient
        colors={['transparent', 'rgba(0,0,0,0.8)']}
        style={styles.gradient}
      />
      
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.name}>{pet.name}</Text>
          <Text style={styles.age}>{pet.age}</Text>
        </View>
        
        <Text style={styles.breed}>{pet.breed}</Text>
        
        <View style={styles.location}>
          <MapPin size={16} color="#FFFFFF" />
          <Text style={styles.locationText}>{pet.location}</Text>
        </View>
        
        <View style={styles.footer}>
          <Text style={styles.price}>${pet.price}</Text>
          <Text style={styles.seller}>by {pet.seller}</Text>
        </View>
      </View>
      
      {/* Swipe indicators */}
      <View style={[styles.swipeIndicator, styles.likeIndicator]}>
        <Heart size={40} color="#10B981" fill="#10B981" />
        <Text style={[styles.indicatorText, { color: '#10B981' }]}>LIKE</Text>
      </View>
      
      <View style={[styles.swipeIndicator, styles.passIndicator]}>
        <X size={40} color="#EF4444" />
        <Text style={[styles.indicatorText, { color: '#EF4444' }]}>PASS</Text>
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  card: {
    width: screenWidth - 40,
    height: 600,
    borderRadius: 20,
    overflow: 'hidden',
    backgroundColor: '#FFFFFF',
    elevation: 8,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    position: 'absolute',
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  gradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 200,
  },
  content: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: 24,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'baseline',
    marginBottom: 8,
    gap: 12,
  },
  name: {
    fontSize: 32,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  age: {
    fontSize: 24,
    fontWeight: '400',
    color: '#E5E7EB',
  },
  breed: {
    fontSize: 18,
    color: '#D1D5DB',
    marginBottom: 8,
  },
  location: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: 16,
  },
  locationText: {
    fontSize: 16,
    color: '#FFFFFF',
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  price: {
    fontSize: 24,
    fontWeight: '700',
    color: '#F97316',
  },
  seller: {
    fontSize: 14,
    color: '#D1D5DB',
    fontStyle: 'italic',
  },
  swipeIndicator: {
    position: 'absolute',
    top: 100,
    alignItems: 'center',
    opacity: 0,
    transform: [{ rotate: '-30deg' }],
  },
  likeIndicator: {
    right: 40,
  },
  passIndicator: {
    left: 40,
    transform: [{ rotate: '30deg' }],
  },
  indicatorText: {
    fontSize: 32,
    fontWeight: '800',
    marginTop: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.3)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
});