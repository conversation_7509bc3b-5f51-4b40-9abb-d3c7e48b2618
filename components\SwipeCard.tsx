import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Image,
  Dimensions,
} from 'react-native';
import { MapPin, Heart, X, Star, Shield, Award } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import Animated from 'react-native-reanimated';
import { Colors, Typography, Spacing, BorderRadius, Shadows } from '@/constants/Design';
import { Pet } from '@/types/Pet';

const { width: screenWidth } = Dimensions.get('window');

interface SwipeCardProps {
  pet: Pet;
  style?: any;
}

export default function SwipeCard({ pet, style }: SwipeCardProps) {
  return (
    <Animated.View style={[styles.card, style]}>
      <Image source={{ uri: pet.image }} style={styles.image} />

      {/* Status badges */}
      <View style={styles.badges}>
        {pet.vaccinated && (
          <View style={[styles.badge, styles.vaccinatedBadge]}>
            <Shield size={12} color={Colors.accent.green[600]} />
            <Text style={styles.badgeText}>Vaccinated</Text>
          </View>
        )}
        {pet.trained && (
          <View style={[styles.badge, styles.trainedBadge]}>
            <Award size={12} color={Colors.primary[600]} />
            <Text style={styles.badgeText}>Trained</Text>
          </View>
        )}
      </View>

      <LinearGradient
        colors={['transparent', 'rgba(0,0,0,0.7)']}
        style={styles.gradient}
      />

      <View style={styles.content}>
        <View style={styles.header}>
          <View style={styles.nameSection}>
            <Text style={styles.name}>{pet.name}</Text>
            <View style={styles.ageGender}>
              <Text style={styles.age}>{pet.age}</Text>
              {pet.gender && (
                <>
                  <View style={styles.dot} />
                  <Text style={styles.gender}>{pet.gender}</Text>
                </>
              )}
            </View>
          </View>
          <View style={styles.priceContainer}>
            <Text style={styles.price}>${pet.price}</Text>
          </View>
        </View>

        <Text style={styles.breed}>{pet.breed}</Text>

        <View style={styles.location}>
          <MapPin size={14} color={Colors.neutral[300]} />
          <Text style={styles.locationText}>{pet.location}</Text>
        </View>

        <View style={styles.footer}>
          <Text style={styles.seller}>Listed by {pet.seller}</Text>
          {pet.weight && (
            <Text style={styles.weight}>{pet.weight}</Text>
          )}
        </View>
      </View>

      {/* Swipe indicators */}
      <View style={[styles.swipeIndicator, styles.likeIndicator]}>
        <Heart size={40} color={Colors.accent.green[500]} fill={Colors.accent.green[500]} />
        <Text style={[styles.indicatorText, { color: Colors.accent.green[500] }]}>LOVE</Text>
      </View>

      <View style={[styles.swipeIndicator, styles.passIndicator]}>
        <X size={40} color={Colors.error} />
        <Text style={[styles.indicatorText, { color: Colors.error }]}>PASS</Text>
      </View>
    </Animated.View>
  );
}

const styles = StyleSheet.create({
  card: {
    width: screenWidth - Spacing['3xl'],
    height: 620,
    borderRadius: BorderRadius['3xl'],
    overflow: 'hidden',
    backgroundColor: Colors.background.primary,
    ...Shadows.xl,
    position: 'absolute',
  },
  image: {
    width: '100%',
    height: '100%',
    resizeMode: 'cover',
  },
  badges: {
    position: 'absolute',
    top: Spacing.lg,
    left: Spacing.lg,
    zIndex: 10,
    flexDirection: 'row',
    gap: Spacing.sm,
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.full,
    gap: 4,
  },
  vaccinatedBadge: {
    backgroundColor: Colors.accent.green[50],
    borderWidth: 1,
    borderColor: Colors.accent.green[200],
  },
  trainedBadge: {
    backgroundColor: Colors.primary[50],
    borderWidth: 1,
    borderColor: Colors.primary[200],
  },
  badgeText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.primary,
  },
  gradient: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    height: 220,
  },
  content: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    padding: Spacing['2xl'],
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.md,
  },
  nameSection: {
    flex: 1,
  },
  name: {
    fontSize: Typography.fontSize['4xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.inverse,
    marginBottom: 4,
  },
  ageGender: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
  },
  age: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.neutral[200],
  },
  dot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: Colors.neutral[300],
  },
  gender: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.neutral[200],
    textTransform: 'capitalize',
  },
  priceContainer: {
    backgroundColor: Colors.primary[500],
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.full,
  },
  price: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.inverse,
  },
  breed: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.neutral[100],
    marginBottom: Spacing.md,
  },
  location: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: Spacing.lg,
  },
  locationText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.medium,
    color: Colors.neutral[200],
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  seller: {
    fontSize: Typography.fontSize.sm,
    color: Colors.neutral[300],
    fontStyle: 'italic',
  },
  weight: {
    fontSize: Typography.fontSize.sm,
    color: Colors.neutral[300],
    fontWeight: Typography.fontWeight.medium,
  },
  swipeIndicator: {
    position: 'absolute',
    top: 120,
    alignItems: 'center',
    opacity: 0,
    backgroundColor: Colors.background.primary,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.full,
    ...Shadows.lg,
  },
  likeIndicator: {
    right: Spacing['2xl'],
    transform: [{ rotate: '-15deg' }],
  },
  passIndicator: {
    left: Spacing['2xl'],
    transform: [{ rotate: '15deg' }],
  },
  indicatorText: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.extrabold,
    marginTop: 4,
    letterSpacing: 1,
  },
});