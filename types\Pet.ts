export interface Pet {
  id: string;
  name: string;
  breed: string;
  age: string;
  price: number;
  image: string;
  type: 'dog' | 'cat';
  location: string;
  seller: string;
  description?: string;
  images?: string[];
  vaccinated?: boolean;
  trained?: boolean;
  gender?: 'male' | 'female';
  weight?: string;
  healthCertificate?: boolean;
}

export interface User {
  id: string;
  name: string;
  email: string;
  phone: string;
  location: string;
  avatar: string;
  rating: number;
  reviewCount: number;
  isVerified: boolean;
  joinDate: string;
}

export interface Filter {
  type: 'all' | 'dog' | 'cat';
  breeds: string[];
  priceRange: {
    min: string;
    max: string;
  };
  ageRange: 'all' | 'puppy' | 'adult' | 'senior';
  location?: string;
}