import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Modal,
  TextInput,
  ScrollView,
} from 'react-native';
import { X, Check } from 'lucide-react-native';

interface FilterModalProps {
  visible: boolean;
  onClose: () => void;
  onApplyFilters: (filters: any) => void;
}

const BREEDS = {
  dog: ['Golden Retriever', 'French Bulldog', 'Labrador', 'German Shepherd', 'Poodle'],
  cat: ['Persian', 'Maine Coon', 'Siamese', 'British Shorthair', 'Ragdoll'],
};

export default function FilterModal({ visible, onClose, onApplyFilters }: FilterModalProps) {
  const [selectedType, setSelectedType] = useState<'all' | 'dog' | 'cat'>('all');
  const [selectedBreeds, setSelectedBreeds] = useState<string[]>([]);
  const [priceRange, setPriceRange] = useState({ min: '', max: '' });
  const [ageRange, setAgeRange] = useState<'all' | 'puppy' | 'adult' | 'senior'>('all');

  const handleApplyFilters = () => {
    onApplyFilters({
      type: selectedType,
      breeds: selectedBreeds,
      priceRange,
      ageRange,
    });
    onClose();
  };

  const toggleBreed = (breed: string) => {
    setSelectedBreeds(prev =>
      prev.includes(breed)
        ? prev.filter(b => b !== breed)
        : [...prev, breed]
    );
  };

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose}>
            <X size={24} color="#374151" />
          </TouchableOpacity>
          <Text style={styles.title}>Filters</Text>
          <TouchableOpacity onPress={handleApplyFilters} style={styles.applyButton}>
            <Text style={styles.applyButtonText}>Apply</Text>
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Pet Type</Text>
            <View style={styles.typeSelector}>
              {['all', 'dog', 'cat'].map((type) => (
                <TouchableOpacity
                  key={type}
                  style={[
                    styles.typeButton,
                    selectedType === type && styles.activeTypeButton,
                  ]}
                  onPress={() => setSelectedType(type as any)}
                >
                  <Text
                    style={[
                      styles.typeButtonText,
                      selectedType === type && styles.activeTypeButtonText,
                    ]}
                  >
                    {type === 'all' ? 'All Pets' : type === 'dog' ? 'Dogs' : 'Cats'}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Price Range</Text>
            <View style={styles.priceInputs}>
              <View style={styles.priceInputContainer}>
                <Text style={styles.priceLabel}>Min</Text>
                <TextInput
                  style={styles.priceInput}
                  value={priceRange.min}
                  onChangeText={(text) => setPriceRange(prev => ({ ...prev, min: text }))}
                  placeholder="$0"
                  keyboardType="numeric"
                  placeholderTextColor="#9CA3AF"
                />
              </View>
              <Text style={styles.priceSeparator}>to</Text>
              <View style={styles.priceInputContainer}>
                <Text style={styles.priceLabel}>Max</Text>
                <TextInput
                  style={styles.priceInput}
                  value={priceRange.max}
                  onChangeText={(text) => setPriceRange(prev => ({ ...prev, max: text }))}
                  placeholder="$5000"
                  keyboardType="numeric"
                  placeholderTextColor="#9CA3AF"
                />
              </View>
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Age</Text>
            <View style={styles.ageSelector}>
              {['all', 'puppy', 'adult', 'senior'].map((age) => (
                <TouchableOpacity
                  key={age}
                  style={[
                    styles.ageButton,
                    ageRange === age && styles.activeAgeButton,
                  ]}
                  onPress={() => setAgeRange(age as any)}
                >
                  <Text
                    style={[
                      styles.ageButtonText,
                      ageRange === age && styles.activeAgeButtonText,
                    ]}
                  >
                    {age.charAt(0).toUpperCase() + age.slice(1)}
                  </Text>
                </TouchableOpacity>
              ))}
            </View>
          </View>

          {selectedType !== 'all' && (
            <View style={styles.section}>
              <Text style={styles.sectionTitle}>Breeds</Text>
              <View style={styles.breedGrid}>
                {BREEDS[selectedType].map((breed) => (
                  <TouchableOpacity
                    key={breed}
                    style={[
                      styles.breedButton,
                      selectedBreeds.includes(breed) && styles.activeBreedButton,
                    ]}
                    onPress={() => toggleBreed(breed)}
                  >
                    {selectedBreeds.includes(breed) && (
                      <Check size={16} color="#FFFFFF" />
                    )}
                    <Text
                      style={[
                        styles.breedButtonText,
                        selectedBreeds.includes(breed) && styles.activeBreedButtonText,
                      ]}
                    >
                      {breed}
                    </Text>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          )}
        </ScrollView>
      </View>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  title: {
    fontSize: 20,
    fontWeight: '700',
    color: '#111827',
  },
  applyButton: {
    backgroundColor: '#F97316',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
  },
  applyButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
  },
  section: {
    marginVertical: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: '700',
    color: '#111827',
    marginBottom: 12,
  },
  typeSelector: {
    flexDirection: 'row',
    gap: 8,
  },
  typeButton: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 12,
    backgroundColor: '#F3F4F6',
    alignItems: 'center',
  },
  activeTypeButton: {
    backgroundColor: '#F97316',
  },
  typeButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
  },
  activeTypeButtonText: {
    color: '#FFFFFF',
  },
  priceInputs: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  priceInputContainer: {
    flex: 1,
  },
  priceLabel: {
    fontSize: 14,
    fontWeight: '600',
    color: '#374151',
    marginBottom: 8,
  },
  priceInput: {
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderWidth: 1,
    borderColor: '#E5E7EB',
    fontSize: 16,
    color: '#111827',
  },
  priceSeparator: {
    fontSize: 16,
    color: '#6B7280',
    marginTop: 24,
  },
  ageSelector: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  ageButton: {
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
  },
  activeAgeButton: {
    backgroundColor: '#3B82F6',
  },
  ageButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
  },
  activeAgeButtonText: {
    color: '#FFFFFF',
  },
  breedGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  breedButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 12,
    borderRadius: 20,
    backgroundColor: '#F3F4F6',
    gap: 6,
  },
  activeBreedButton: {
    backgroundColor: '#10B981',
  },
  breedButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
  },
  activeBreedButtonText: {
    color: '#FFFFFF',
  },
});