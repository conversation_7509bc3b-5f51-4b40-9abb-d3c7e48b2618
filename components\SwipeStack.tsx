import React, { useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Text,
  Alert,
} from 'react-native';
import {
  PanGestureHandler,
  PanGestureHandlerGestureEvent,
} from 'react-native-gesture-handler';
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  runOnJS,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import { Heart, X, RotateCcw } from 'lucide-react-native';
import SwipeCard from './SwipeCard';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface Pet {
  id: string;
  name: string;
  breed: string;
  age: string;
  price: number;
  image: string;
  type: 'dog' | 'cat';
  location: string;
  seller: string;
}

interface SwipeStackProps {
  pets: Pet[];
  onLike: (pet: Pet) => void;
  onPass: (pet: Pet) => void;
}

const SWIPE_THRESHOLD = screenWidth * 0.3;

export default function SwipeStack({ pets, onLike, onPass }: SwipeStackProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const scale = useSharedValue(1);

  const currentPet = pets[currentIndex];
  const nextPet = pets[currentIndex + 1];

  const handleSwipeComplete = (direction: 'left' | 'right') => {
    if (currentPet) {
      if (direction === 'right') {
        onLike(currentPet);
      } else {
        onPass(currentPet);
      }
    }
    
    setCurrentIndex(prev => prev + 1);
    translateX.value = 0;
    translateY.value = 0;
    scale.value = 1;
  };

  const gestureHandler = useAnimatedGestureHandler<PanGestureHandlerGestureEvent>({
    onStart: () => {
      scale.value = withSpring(0.95);
    },
    onActive: (event) => {
      translateX.value = event.translationX;
      translateY.value = event.translationY;
    },
    onEnd: (event) => {
      const shouldSwipeRight = translateX.value > SWIPE_THRESHOLD;
      const shouldSwipeLeft = translateX.value < -SWIPE_THRESHOLD;

      if (shouldSwipeRight) {
        translateX.value = withSpring(screenWidth);
        runOnJS(handleSwipeComplete)('right');
      } else if (shouldSwipeLeft) {
        translateX.value = withSpring(-screenWidth);
        runOnJS(handleSwipeComplete)('left');
      } else {
        translateX.value = withSpring(0);
        translateY.value = withSpring(0);
        scale.value = withSpring(1);
      }
    },
  });

  const cardStyle = useAnimatedStyle(() => {
    const rotation = interpolate(
      translateX.value,
      [-screenWidth, 0, screenWidth],
      [-30, 0, 30],
      Extrapolate.CLAMP
    );

    const likeOpacity = interpolate(
      translateX.value,
      [0, SWIPE_THRESHOLD],
      [0, 1],
      Extrapolate.CLAMP
    );

    const passOpacity = interpolate(
      translateX.value,
      [-SWIPE_THRESHOLD, 0],
      [1, 0],
      Extrapolate.CLAMP
    );

    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { rotate: `${rotation}deg` },
        { scale: scale.value },
      ],
    };
  });

  const likeIndicatorStyle = useAnimatedStyle(() => ({
    opacity: interpolate(
      translateX.value,
      [0, SWIPE_THRESHOLD],
      [0, 1],
      Extrapolate.CLAMP
    ),
  }));

  const passIndicatorStyle = useAnimatedStyle(() => ({
    opacity: interpolate(
      translateX.value,
      [-SWIPE_THRESHOLD, 0],
      [1, 0],
      Extrapolate.CLAMP
    ),
  }));

  const handleLike = () => {
    translateX.value = withSpring(screenWidth);
    handleSwipeComplete('right');
  };

  const handlePass = () => {
    translateX.value = withSpring(-screenWidth);
    handleSwipeComplete('left');
  };

  const handleRewind = () => {
    if (currentIndex > 0) {
      setCurrentIndex(prev => prev - 1);
    }
  };

  if (currentIndex >= pets.length) {
    return (
      <View style={styles.emptyState}>
        <Text style={styles.emptyTitle}>No More Pets!</Text>
        <Text style={styles.emptySubtitle}>
          You've seen all available pets. Check back later for new listings!
        </Text>
        <TouchableOpacity
          style={styles.resetButton}
          onPress={() => setCurrentIndex(0)}
        >
          <Text style={styles.resetButtonText}>Start Over</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.cardStack}>
        {/* Next card (background) */}
        {nextPet && (
          <SwipeCard
            pet={nextPet}
            style={[styles.nextCard]}
          />
        )}

        {/* Current card */}
        {currentPet && (
          <PanGestureHandler onGestureEvent={gestureHandler}>
            <Animated.View style={cardStyle}>
              <SwipeCard pet={currentPet} />
              
              {/* Animated indicators */}
              <Animated.View style={[styles.likeIndicator, likeIndicatorStyle]}>
                <Heart size={40} color="#10B981" fill="#10B981" />
                <Text style={[styles.indicatorText, { color: '#10B981' }]}>LIKE</Text>
              </Animated.View>
              
              <Animated.View style={[styles.passIndicator, passIndicatorStyle]}>
                <X size={40} color="#EF4444" />
                <Text style={[styles.indicatorText, { color: '#EF4444' }]}>PASS</Text>
              </Animated.View>
            </Animated.View>
          </PanGestureHandler>
        )}
      </View>

      {/* Action buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.actionButton, styles.rewindButton]}
          onPress={handleRewind}
          disabled={currentIndex === 0}
        >
          <RotateCcw size={24} color={currentIndex === 0 ? '#9CA3AF' : '#6B7280'} />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.passButton]}
          onPress={handlePass}
        >
          <X size={28} color="#EF4444" />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.likeButton]}
          onPress={handleLike}
        >
          <Heart size={28} color="#10B981" />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cardStack: {
    width: screenWidth - 40,
    height: 600,
    alignItems: 'center',
    justifyContent: 'center',
  },
  nextCard: {
    transform: [{ scale: 0.95 }],
    opacity: 0.8,
  },
  likeIndicator: {
    position: 'absolute',
    top: 100,
    right: 40,
    alignItems: 'center',
    transform: [{ rotate: '-30deg' }],
  },
  passIndicator: {
    position: 'absolute',
    top: 100,
    left: 40,
    alignItems: 'center',
    transform: [{ rotate: '30deg' }],
  },
  indicatorText: {
    fontSize: 32,
    fontWeight: '800',
    marginTop: 8,
    textShadowColor: 'rgba(0, 0, 0, 0.5)',
    textShadowOffset: { width: 2, height: 2 },
    textShadowRadius: 4,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 40,
    gap: 20,
  },
  actionButton: {
    width: 60,
    height: 60,
    borderRadius: 30,
    alignItems: 'center',
    justifyContent: 'center',
    elevation: 4,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  rewindButton: {
    backgroundColor: '#F3F4F6',
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  passButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#EF4444',
  },
  likeButton: {
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#10B981',
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#111827',
    marginBottom: 12,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 32,
  },
  resetButton: {
    backgroundColor: '#F97316',
    borderRadius: 12,
    paddingHorizontal: 24,
    paddingVertical: 12,
  },
  resetButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});