import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  Alert,
} from 'react-native';
import { Filter, Settings } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import SwipeStack from '@/components/SwipeStack';
import { SAMPLE_PETS } from '@/data/mockData';

export default function BrowseScreen() {
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [favorites, setFavorites] = useState<string[]>([]);

  const filteredPets = SAMPLE_PETS.filter(pet => {
    const matchesFilter = selectedFilter === 'all' || pet.type === selectedFilter;
    return matchesFilter;
  });

  const handleLike = (pet: any) => {
    setFavorites(prev => [...prev, pet.id]);
    Alert.alert('❤️ Liked!', `You liked ${pet.name}! Check your favorites to contact the seller.`);
  };

  const handlePass = (pet: any) => {
    // Just pass, no action needed
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#F97316', '#FB923C']}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <View>
            <Text style={styles.headerTitle}>Discover Pets</Text>
            <Text style={styles.headerSubtitle}>Swipe to find your perfect companion</Text>
          </View>
          <TouchableOpacity style={styles.settingsButton}>
            <Settings size={24} color="#FFFFFF" />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <View style={styles.filterTabs}>
        {['all', 'dog', 'cat'].map((filter) => (
          <TouchableOpacity
            key={filter}
            style={[
              styles.filterTab,
              selectedFilter === filter && styles.activeFilterTab,
            ]}
            onPress={() => setSelectedFilter(filter)}
          >
            <Text
              style={[
                styles.filterTabText,
                selectedFilter === filter && styles.activeFilterTabText,
              ]}
            >
              {filter === 'all' ? 'All Pets' : filter === 'dog' ? 'Dogs' : 'Cats'}
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <View style={styles.swipeContainer}>
        <SwipeStack
          pets={filteredPets}
          onLike={handleLike}
          onPass={handlePass}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    paddingHorizontal: 24,
    paddingVertical: 32,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#FED7AA',
  },
  settingsButton: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 12,
    opacity: 0.9,
  },
  swipeContainer: {
    flex: 1,
    paddingVertical: 20,
  },
  filterTabs: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    paddingVertical: 12,
    gap: 12,
    justifyContent: 'center',
  },
  filterTab: {
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  activeFilterTab: {
    backgroundColor: '#F97316',
    borderColor: '#F97316',
  },
  filterTabText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#6B7280',
  },
  activeFilterTabText: {
    color: '#FFFFFF',
  },
});