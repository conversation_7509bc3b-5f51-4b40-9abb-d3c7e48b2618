import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
  StatusBar,
} from 'react-native';
import { Heart, MapPin, MessageCircle, Star, Shield, Award } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Colors, Typography, Spacing, BorderRadius, Shadows, Gradients } from '@/constants/Design';

const FAVORITE_PETS = [
  {
    id: '1',
    name: '<PERSON>',
    breed: 'Golden Retriever',
    age: '2 years',
    price: 800,
    image: 'https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg',
    type: 'dog',
    location: 'New York, NY',
    seller: '<PERSON>',
    savedDate: '2 days ago',
  },
  {
    id: '4',
    name: 'Whiskers',
    breed: 'Maine Coon',
    age: '2 years',
    price: 700,
    image: 'https://images.pexels.com/photos/208984/pexels-photo-208984.jpeg',
    type: 'cat',
    location: 'Miami, FL',
    seller: '<PERSON>',
    savedDate: '1 week ago',
  },
];

export default function FavoritesScreen() {
  const [favorites, setFavorites] = useState(FAVORITE_PETS);

  const removeFavorite = (petId: string) => {
    setFavorites(favorites.filter(pet => pet.id !== petId));
  };

  if (favorites.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <StatusBar barStyle="light-content" backgroundColor={Colors.accent.pink[500]} />

        <LinearGradient
          colors={Gradients.accent}
          style={styles.header}
        >
          <Text style={styles.headerTitle}>Your Favorites</Text>
          <Text style={styles.headerSubtitle}>Pets you've saved for later</Text>
        </LinearGradient>

        <View style={styles.emptyState}>
          <View style={styles.emptyIcon}>
            <Heart size={80} color={Colors.accent.pink[200]} />
          </View>
          <Text style={styles.emptyTitle}>No Favorites Yet</Text>
          <Text style={styles.emptySubtitle}>
            Start browsing pets and swipe right or tap the heart icon to save your favorites here
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.accent.pink[500]} />

      <LinearGradient
        colors={Gradients.accent}
        style={styles.header}
      >
        <Text style={styles.headerTitle}>Your Favorites</Text>
        <Text style={styles.headerSubtitle}>
          {favorites.length} {favorites.length === 1 ? 'pet' : 'pets'} saved
        </Text>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {favorites.map((pet) => (
          <View key={pet.id} style={styles.petCard}>
            <Image source={{ uri: pet.image }} style={styles.petImage} />

            {/* Badges */}
            <View style={styles.badges}>
              <View style={[styles.badge, styles.vaccinatedBadge]}>
                <Shield size={12} color={Colors.accent.green[600]} />
                <Text style={styles.badgeText}>Vaccinated</Text>
              </View>
              <View style={[styles.badge, styles.trainedBadge]}>
                <Award size={12} color={Colors.primary[600]} />
                <Text style={styles.badgeText}>Trained</Text>
              </View>
            </View>

            <View style={styles.petInfo}>
              <View style={styles.petHeader}>
                <View style={styles.petTitleSection}>
                  <Text style={styles.petName}>{pet.name}</Text>
                  <View style={styles.petMeta}>
                    <Text style={styles.petAge}>{pet.age}</Text>
                    <View style={styles.dot} />
                    <Text style={styles.petBreed}>{pet.breed}</Text>
                  </View>
                  <Text style={styles.savedDate}>Saved {pet.savedDate}</Text>
                </View>
                <TouchableOpacity
                  style={styles.heartButton}
                  onPress={() => removeFavorite(pet.id)}
                >
                  <Heart size={22} color={Colors.accent.pink[500]} fill={Colors.accent.pink[500]} />
                </TouchableOpacity>
              </View>

              <View style={styles.petLocation}>
                <MapPin size={16} color={Colors.text.secondary} />
                <Text style={styles.locationText}>{pet.location}</Text>
              </View>

              <View style={styles.petFooter}>
                <Text style={styles.petPrice}>${pet.price}</Text>
                <TouchableOpacity style={styles.contactButton}>
                  <MessageCircle size={18} color={Colors.text.inverse} />
                  <Text style={styles.contactButtonText}>Contact Seller</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },
  header: {
    paddingHorizontal: Spacing['2xl'],
    paddingTop: Spacing['4xl'],
    paddingBottom: Spacing['3xl'],
    borderBottomLeftRadius: BorderRadius['3xl'],
    borderBottomRightRadius: BorderRadius['3xl'],
  },
  headerTitle: {
    fontSize: Typography.fontSize['4xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.inverse,
    marginBottom: Spacing.sm,
  },
  headerSubtitle: {
    fontSize: Typography.fontSize.lg,
    color: Colors.accent.pink[100],
    fontWeight: Typography.fontWeight.medium,
  },
  content: {
    flex: 1,
    paddingHorizontal: Spacing['2xl'],
    paddingTop: Spacing.lg,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: Spacing['4xl'],
  },
  emptyIcon: {
    marginBottom: Spacing['2xl'],
  },
  emptyTitle: {
    fontSize: Typography.fontSize['3xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.primary,
    marginBottom: Spacing.lg,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: Typography.lineHeight.relaxed * Typography.fontSize.base,
  },
  petCard: {
    backgroundColor: Colors.background.primary,
    borderRadius: BorderRadius['2xl'],
    marginBottom: Spacing.lg,
    overflow: 'hidden',
    ...Shadows.md,
    position: 'relative',
  },
  petImage: {
    width: '100%',
    height: 220,
    resizeMode: 'cover',
  },
  badges: {
    position: 'absolute',
    top: Spacing.lg,
    left: Spacing.lg,
    flexDirection: 'row',
    gap: Spacing.sm,
    zIndex: 10,
  },
  badge: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
    paddingVertical: Spacing.sm,
    borderRadius: BorderRadius.full,
    gap: 4,
  },
  vaccinatedBadge: {
    backgroundColor: Colors.accent.green[50],
    borderWidth: 1,
    borderColor: Colors.accent.green[200],
  },
  trainedBadge: {
    backgroundColor: Colors.primary[50],
    borderWidth: 1,
    borderColor: Colors.primary[200],
  },
  badgeText: {
    fontSize: Typography.fontSize.xs,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.primary,
  },
  petInfo: {
    padding: Spacing.lg,
  },
  petHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.md,
  },
  petTitleSection: {
    flex: 1,
  },
  petName: {
    fontSize: Typography.fontSize['2xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.primary,
    marginBottom: 4,
  },
  petMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.sm,
    marginBottom: 4,
  },
  petAge: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    fontWeight: Typography.fontWeight.medium,
  },
  dot: {
    width: 4,
    height: 4,
    borderRadius: 2,
    backgroundColor: Colors.neutral[300],
  },
  petBreed: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    fontWeight: Typography.fontWeight.medium,
  },
  savedDate: {
    fontSize: Typography.fontSize.xs,
    color: Colors.text.tertiary,
    fontStyle: 'italic',
  },
  heartButton: {
    padding: Spacing.sm,
  },
  petLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    marginBottom: Spacing.lg,
  },
  locationText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.text.secondary,
    fontWeight: Typography.fontWeight.medium,
  },
  petFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  petPrice: {
    fontSize: Typography.fontSize.xl,
    fontWeight: Typography.fontWeight.bold,
    color: Colors.primary[500],
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.secondary[500],
    borderRadius: BorderRadius.full,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    gap: Spacing.sm,
    ...Shadows.sm,
  },
  contactButtonText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.inverse,
  },
});