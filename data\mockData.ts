import { Pet, User } from '@/types/Pet';

export const SAMPLE_PETS: Pet[] = [
  {
    id: '1',
    name: '<PERSON>',
    breed: 'Golden Retriever',
    age: '2 years',
    price: 800,
    image: 'https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg',
    type: 'dog',
    location: 'New York, NY',
    seller: '<PERSON>',
    description: '<PERSON> is a friendly and energetic Golden Retriever who loves playing fetch and swimming. He\'s great with kids and other dogs.',
    vaccinated: true,
    trained: true,
    gender: 'male',
    weight: '65 lbs',
    healthCertificate: true,
  },
  {
    id: '2',
    name: '<PERSON>',
    breed: 'Persian Cat',
    age: '1 year',
    price: 600,
    image: 'https://images.pexels.com/photos/45201/kitty-cat-kitten-pet-45201.jpeg',
    type: 'cat',
    location: 'Los Angeles, CA',
    seller: '<PERSON>',
    description: '<PERSON> is a gentle Persian cat with beautiful long fur. She loves to cuddle and is perfect for a quiet home.',
    vaccinated: true,
    trained: true,
    gender: 'female',
    weight: '8 lbs',
    healthCertificate: true,
  },
  {
    id: '3',
    name: '<PERSON>',
    breed: 'French Bulldog',
    age: '3 years',
    price: 1200,
    image: 'https://images.pexels.com/photos/1805164/pexels-photo-1805164.jpeg',
    type: 'dog',
    location: 'Chicago, IL',
    seller: 'Mike Wilson',
    description: 'Charlie is a charming French Bulldog with a great personality. He\'s calm, friendly, and loves attention.',
    vaccinated: true,
    trained: true,
    gender: 'male',
    weight: '25 lbs',
    healthCertificate: true,
  },
  {
    id: '4',
    name: 'Whiskers',
    breed: 'Maine Coon',
    age: '2 years',
    price: 700,
    image: 'https://images.pexels.com/photos/208984/pexels-photo-208984.jpeg',
    type: 'cat',
    location: 'Miami, FL',
    seller: 'Emily Davis',
    description: 'Whiskers is a majestic Maine Coon with a gentle temperament. He\'s very social and loves being around people.',
    vaccinated: true,
    trained: true,
    gender: 'male',
    weight: '15 lbs',
    healthCertificate: true,
  },
  {
    id: '5',
    name: 'Bella',
    breed: 'Labrador',
    age: '1 year',
    price: 900,
    image: 'https://images.pexels.com/photos/1254140/pexels-photo-1254140.jpeg',
    type: 'dog',
    location: 'Austin, TX',
    seller: 'David Brown',
    description: 'Bella is a young Labrador full of energy and love. She\'s perfect for an active family who enjoys outdoor activities.',
    vaccinated: true,
    trained: false,
    gender: 'female',
    weight: '45 lbs',
    healthCertificate: true,
  },
  {
    id: '6',
    name: 'Mittens',
    breed: 'Siamese',
    age: '6 months',
    price: 450,
    image: 'https://images.pexels.com/photos/416160/pexels-photo-416160.jpeg',
    type: 'cat',
    location: 'Seattle, WA',
    seller: 'Lisa Chen',
    description: 'Mittens is a playful Siamese kitten with striking blue eyes. She\'s very vocal and loves interactive play.',
    vaccinated: true,
    trained: true,
    gender: 'female',
    weight: '4 lbs',
    healthCertificate: true,
  },
];

export const SAMPLE_USERS: User[] = [
  {
    id: '1',
    name: 'John Smith',
    email: '<EMAIL>',
    phone: '(*************',
    location: 'New York, NY',
    avatar: 'https://images.pexels.com/photos/220453/pexels-photo-220453.jpeg',
    rating: 4.9,
    reviewCount: 15,
    isVerified: true,
    joinDate: '2022-03-15',
  },
  {
    id: '2',
    name: 'Sarah Johnson',
    email: '<EMAIL>',
    phone: '(*************',
    location: 'Los Angeles, CA',
    avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg',
    rating: 4.8,
    reviewCount: 12,
    isVerified: true,
    joinDate: '2021-11-22',
  },
];