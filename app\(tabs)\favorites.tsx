import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Image,
  SafeAreaView,
} from 'react-native';
import { Heart, MapPin, MessageCircle } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';

const FAVORITE_PETS = [
  {
    id: '1',
    name: '<PERSON>',
    breed: 'Golden Retriever',
    age: '2 years',
    price: 800,
    image: 'https://images.pexels.com/photos/1108099/pexels-photo-1108099.jpeg',
    type: 'dog',
    location: 'New York, NY',
    seller: '<PERSON>',
    savedDate: '2 days ago',
  },
  {
    id: '4',
    name: 'Whiskers',
    breed: '<PERSON> Coon',
    age: '2 years',
    price: 700,
    image: 'https://images.pexels.com/photos/208984/pexels-photo-208984.jpeg',
    type: 'cat',
    location: 'Miami, FL',
    seller: '<PERSON>',
    savedDate: '1 week ago',
  },
];

export default function FavoritesScreen() {
  const [favorites, setFavorites] = useState(FAVORITE_PETS);

  const removeFavorite = (petId: string) => {
    setFavorites(favorites.filter(pet => pet.id !== petId));
  };

  if (favorites.length === 0) {
    return (
      <SafeAreaView style={styles.container}>
        <LinearGradient
          colors={['#EC4899', '#DB2777']}
          style={styles.header}
        >
          <Text style={styles.headerTitle}>Your Favorites</Text>
          <Text style={styles.headerSubtitle}>Pets you've saved for later</Text>
        </LinearGradient>

        <View style={styles.emptyState}>
          <Heart size={64} color="#E5E7EB" />
          <Text style={styles.emptyTitle}>No Favorites Yet</Text>
          <Text style={styles.emptySubtitle}>
            Start browsing pets and tap the heart icon to save your favorites
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#EC4899', '#DB2777']}
        style={styles.header}
      >
        <Text style={styles.headerTitle}>Your Favorites</Text>
        <Text style={styles.headerSubtitle}>
          {favorites.length} {favorites.length === 1 ? 'pet' : 'pets'} saved
        </Text>
      </LinearGradient>

      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {favorites.map((pet) => (
          <View key={pet.id} style={styles.petCard}>
            <Image source={{ uri: pet.image }} style={styles.petImage} />
            <View style={styles.petInfo}>
              <View style={styles.petHeader}>
                <View style={styles.petTitleSection}>
                  <Text style={styles.petName}>{pet.name}</Text>
                  <Text style={styles.savedDate}>Saved {pet.savedDate}</Text>
                </View>
                <TouchableOpacity
                  style={styles.heartButton}
                  onPress={() => removeFavorite(pet.id)}
                >
                  <Heart size={20} color="#EC4899" fill="#EC4899" />
                </TouchableOpacity>
              </View>
              
              <Text style={styles.petBreed}>{pet.breed}</Text>
              <Text style={styles.petAge}>{pet.age}</Text>
              
              <View style={styles.petLocation}>
                <MapPin size={14} color="#6B7280" />
                <Text style={styles.locationText}>{pet.location}</Text>
              </View>
              
              <View style={styles.petFooter}>
                <Text style={styles.petPrice}>${pet.price}</Text>
                <TouchableOpacity style={styles.contactButton}>
                  <MessageCircle size={16} color="#FFFFFF" />
                  <Text style={styles.contactButtonText}>Contact</Text>
                </TouchableOpacity>
              </View>
            </View>
          </View>
        ))}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F9FAFB',
  },
  header: {
    paddingHorizontal: 24,
    paddingVertical: 32,
    borderBottomLeftRadius: 24,
    borderBottomRightRadius: 24,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '700',
    color: '#FFFFFF',
    marginBottom: 8,
  },
  headerSubtitle: {
    fontSize: 16,
    color: '#FCE7F3',
  },
  content: {
    flex: 1,
    paddingHorizontal: 24,
    paddingTop: 16,
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: '700',
    color: '#374151',
    marginTop: 16,
    marginBottom: 8,
  },
  emptySubtitle: {
    fontSize: 16,
    color: '#6B7280',
    textAlign: 'center',
    lineHeight: 24,
  },
  petCard: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    marginBottom: 16,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
  },
  petImage: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  petInfo: {
    padding: 16,
  },
  petHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: 8,
  },
  petTitleSection: {
    flex: 1,
  },
  petName: {
    fontSize: 20,
    fontWeight: '700',
    color: '#111827',
    marginBottom: 4,
  },
  savedDate: {
    fontSize: 12,
    color: '#9CA3AF',
  },
  heartButton: {
    padding: 8,
  },
  petBreed: {
    fontSize: 16,
    color: '#4B5563',
    marginBottom: 4,
  },
  petAge: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
  },
  petLocation: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 12,
  },
  locationText: {
    fontSize: 14,
    color: '#6B7280',
  },
  petFooter: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  petPrice: {
    fontSize: 18,
    fontWeight: '700',
    color: '#F97316',
  },
  contactButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#3B82F6',
    borderRadius: 8,
    paddingHorizontal: 16,
    paddingVertical: 8,
    gap: 6,
  },
  contactButtonText: {
    fontSize: 14,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});