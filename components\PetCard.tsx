import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
} from 'react-native';
import { Heart, MapPin } from 'lucide-react-native';

interface Pet {
  id: string;
  name: string;
  breed: string;
  age: string;
  price: number;
  image: string;
  type: 'dog' | 'cat';
  location: string;
  seller: string;
}

interface PetCardProps {
  pet: Pet;
  onPress?: () => void;
  onFavorite?: () => void;
  isFavorite?: boolean;
}

export default function PetCard({ pet, onPress, onFavorite, isFavorite = false }: PetCardProps) {
  return (
    <TouchableOpacity style={styles.card} onPress={onPress}>
      <View style={styles.imageContainer}>
        <Image source={{ uri: pet.image }} style={styles.image} />
        <TouchableOpacity
          style={styles.favoriteButton}
          onPress={onFavorite}
        >
          <Heart
            size={20}
            color={isFavorite ? '#EC4899' : '#FFFFFF'}
            fill={isFavorite ? '#EC4899' : 'transparent'}
          />
        </TouchableOpacity>
      </View>
      
      <View style={styles.content}>
        <View style={styles.header}>
          <Text style={styles.name}>{pet.name}</Text>
          <Text style={styles.price}>${pet.price}</Text>
        </View>
        
        <Text style={styles.breed}>{pet.breed}</Text>
        <Text style={styles.age}>{pet.age}</Text>
        
        <View style={styles.location}>
          <MapPin size={14} color="#6B7280" />
          <Text style={styles.locationText}>{pet.location}</Text>
        </View>
        
        <Text style={styles.seller}>by {pet.seller}</Text>
      </View>
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  card: {
    backgroundColor: '#FFFFFF',
    borderRadius: 16,
    overflow: 'hidden',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 8,
    marginBottom: 16,
  },
  imageContainer: {
    position: 'relative',
  },
  image: {
    width: '100%',
    height: 200,
    resizeMode: 'cover',
  },
  favoriteButton: {
    position: 'absolute',
    top: 12,
    right: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.3)',
    borderRadius: 20,
    padding: 8,
  },
  content: {
    padding: 16,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  name: {
    fontSize: 20,
    fontWeight: '700',
    color: '#111827',
  },
  price: {
    fontSize: 18,
    fontWeight: '700',
    color: '#F97316',
  },
  breed: {
    fontSize: 16,
    color: '#4B5563',
    marginBottom: 4,
  },
  age: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 8,
  },
  location: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 4,
    marginBottom: 8,
  },
  locationText: {
    fontSize: 14,
    color: '#6B7280',
  },
  seller: {
    fontSize: 14,
    color: '#9CA3AF',
    fontStyle: 'italic',
  },
});