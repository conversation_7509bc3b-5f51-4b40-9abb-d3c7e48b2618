import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Modal,
  ScrollView,
  SafeAreaView,
} from 'react-native';
import { X, MapPin, Calendar, MessageCircle, Phone, Heart } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';

interface Pet {
  id: string;
  name: string;
  breed: string;
  age: string;
  price: number;
  image: string;
  type: 'dog' | 'cat';
  location: string;
  seller: string;
}

interface PetDetailModalProps {
  visible: boolean;
  pet: Pet | null;
  onClose: () => void;
}

export default function PetDetailModal({ visible, pet, onClose }: PetDetailModalProps) {
  if (!pet) return null;

  const additionalImages = [
    'https://images.pexels.com/photos/551628/pexels-photo-551628.jpeg',
    'https://images.pexels.com/photos/406014/pexels-photo-406014.jpeg',
    'https://images.pexels.com/photos/128817/pexels-photo-128817.jpeg',
  ];

  return (
    <Modal visible={visible} animationType="slide" presentationStyle="pageSheet">
      <SafeAreaView style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={onClose} style={styles.closeButton}>
            <X size={24} color="#374151" />
          </TouchableOpacity>
          <TouchableOpacity style={styles.favoriteButton}>
            <Heart size={24} color="#EC4899" />
          </TouchableOpacity>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <Image source={{ uri: pet.image }} style={styles.mainImage} />
          
          <View style={styles.imageGallery}>
            {additionalImages.map((image, index) => (
              <Image key={index} source={{ uri: image }} style={styles.thumbnailImage} />
            ))}
          </View>

          <View style={styles.petInfo}>
            <View style={styles.titleSection}>
              <Text style={styles.petName}>{pet.name}</Text>
              <Text style={styles.petPrice}>${pet.price}</Text>
            </View>

            <Text style={styles.petBreed}>{pet.breed}</Text>
            
            <View style={styles.detailsGrid}>
              <View style={styles.detailItem}>
                <Calendar size={16} color="#6B7280" />
                <Text style={styles.detailText}>{pet.age}</Text>
              </View>
              <View style={styles.detailItem}>
                <MapPin size={16} color="#6B7280" />
                <Text style={styles.detailText}>{pet.location}</Text>
              </View>
            </View>

            <View style={styles.descriptionSection}>
              <Text style={styles.sectionTitle}>About {pet.name}</Text>
              <Text style={styles.description}>
                {pet.name} is a beautiful {pet.breed.toLowerCase()} who loves to play and is great with children. 
                Fully vaccinated, house-trained, and ready for a loving home. Very friendly and social, 
                gets along well with other pets. Comes with all medical records and favorite toys.
              </Text>
            </View>

            <View style={styles.sellerSection}>
              <Text style={styles.sectionTitle}>Seller Information</Text>
              <View style={styles.sellerCard}>
                <Image
                  source={{ uri: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg' }}
                  style={styles.sellerImage}
                />
                <View style={styles.sellerInfo}>
                  <Text style={styles.sellerName}>{pet.seller}</Text>
                  <Text style={styles.sellerRating}>⭐ 4.8 (12 reviews)</Text>
                  <Text style={styles.sellerLocation}>{pet.location}</Text>
                </View>
              </View>
            </View>
          </View>
        </ScrollView>

        <View style={styles.footer}>
          <TouchableOpacity style={styles.contactButton}>
            <MessageCircle size={20} color="#FFFFFF" />
            <Text style={styles.contactButtonText}>Message Seller</Text>
          </TouchableOpacity>
          
          <TouchableOpacity style={styles.callButton}>
            <Phone size={20} color="#3B82F6" />
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 24,
    paddingVertical: 16,
  },
  closeButton: {
    padding: 8,
  },
  favoriteButton: {
    padding: 8,
  },
  content: {
    flex: 1,
  },
  mainImage: {
    width: '100%',
    height: 300,
    resizeMode: 'cover',
  },
  imageGallery: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    paddingVertical: 16,
    gap: 8,
  },
  thumbnailImage: {
    width: 60,
    height: 60,
    borderRadius: 8,
    resizeMode: 'cover',
  },
  petInfo: {
    paddingHorizontal: 24,
  },
  titleSection: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  petName: {
    fontSize: 28,
    fontWeight: '700',
    color: '#111827',
  },
  petPrice: {
    fontSize: 24,
    fontWeight: '700',
    color: '#F97316',
  },
  petBreed: {
    fontSize: 18,
    color: '#4B5563',
    marginBottom: 16,
  },
  detailsGrid: {
    flexDirection: 'row',
    gap: 24,
    marginBottom: 24,
  },
  detailItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  detailText: {
    fontSize: 16,
    color: '#6B7280',
  },
  descriptionSection: {
    marginBottom: 24,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: '700',
    color: '#111827',
    marginBottom: 12,
  },
  description: {
    fontSize: 16,
    color: '#4B5563',
    lineHeight: 24,
  },
  sellerSection: {
    marginBottom: 24,
  },
  sellerCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F9FAFB',
    borderRadius: 12,
    padding: 16,
    gap: 12,
  },
  sellerImage: {
    width: 50,
    height: 50,
    borderRadius: 25,
  },
  sellerInfo: {
    flex: 1,
  },
  sellerName: {
    fontSize: 16,
    fontWeight: '700',
    color: '#111827',
    marginBottom: 2,
  },
  sellerRating: {
    fontSize: 14,
    color: '#6B7280',
    marginBottom: 2,
  },
  sellerLocation: {
    fontSize: 14,
    color: '#9CA3AF',
  },
  footer: {
    flexDirection: 'row',
    paddingHorizontal: 24,
    paddingVertical: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
    gap: 12,
  },
  contactButton: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#3B82F6',
    borderRadius: 12,
    paddingVertical: 16,
    gap: 8,
  },
  contactButtonText: {
    fontSize: 16,
    fontWeight: '700',
    color: '#FFFFFF',
  },
  callButton: {
    alignItems: 'center',
    justifyContent: 'center',
    backgroundColor: '#F3F4F6',
    borderRadius: 12,
    paddingHorizontal: 16,
    paddingVertical: 16,
  },
});