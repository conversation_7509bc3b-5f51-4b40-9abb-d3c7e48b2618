import React, { useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  SafeAreaView,
  Alert,
  StatusBar,
} from 'react-native';
import { Filter, Settings, Sparkles, MapPin } from 'lucide-react-native';
import { LinearGradient } from 'expo-linear-gradient';
import SwipeStack from '@/components/SwipeStack';
import { SAMPLE_PETS } from '@/data/mockData';
import { Colors, Typography, Spacing, BorderRadius, Shadows, Gradients } from '@/constants/Design';

export default function BrowseScreen() {
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [favorites, setFavorites] = useState<string[]>([]);

  const filteredPets = SAMPLE_PETS.filter(pet => {
    const matchesFilter = selectedFilter === 'all' || pet.type === selectedFilter;
    return matchesFilter;
  });

  const handleLike = (pet: any) => {
    setFavorites(prev => [...prev, pet.id]);
    Alert.alert('💕 Amazing!', `You loved ${pet.name}! They've been added to your favorites.`, [
      { text: 'View Favorites', style: 'default' },
      { text: 'Keep Browsing', style: 'cancel' }
    ]);
  };

  const handlePass = (pet: any) => {
    // Just pass, no action needed
  };

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar barStyle="light-content" backgroundColor={Colors.primary[500]} />

      <LinearGradient
        colors={Gradients.sunset}
        style={styles.header}
      >
        <View style={styles.headerContent}>
          <View style={styles.headerText}>
            <View style={styles.titleRow}>
              <Sparkles size={28} color={Colors.text.inverse} />
              <Text style={styles.headerTitle}>Discover</Text>
            </View>
            <Text style={styles.headerSubtitle}>Find your perfect furry companion</Text>
            <View style={styles.locationRow}>
              <MapPin size={16} color={Colors.primary[100]} />
              <Text style={styles.locationText}>New York, NY</Text>
            </View>
          </View>
          <TouchableOpacity style={styles.settingsButton}>
            <Settings size={24} color={Colors.text.inverse} />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      <View style={styles.filterSection}>
        <View style={styles.filterTabs}>
          {[
            { key: 'all', label: 'All Pets', emoji: '🐾' },
            { key: 'dog', label: 'Dogs', emoji: '🐕' },
            { key: 'cat', label: 'Cats', emoji: '🐱' }
          ].map((filter) => (
            <TouchableOpacity
              key={filter.key}
              style={[
                styles.filterTab,
                selectedFilter === filter.key && styles.activeFilterTab,
              ]}
              onPress={() => setSelectedFilter(filter.key)}
            >
              <Text style={styles.filterEmoji}>{filter.emoji}</Text>
              <Text
                style={[
                  styles.filterTabText,
                  selectedFilter === filter.key && styles.activeFilterTabText,
                ]}
              >
                {filter.label}
              </Text>
            </TouchableOpacity>
          ))}
        </View>

        <TouchableOpacity style={styles.filterButton}>
          <Filter size={20} color={Colors.primary[500]} />
        </TouchableOpacity>
      </View>

      <View style={styles.swipeContainer}>
        <SwipeStack
          pets={filteredPets}
          onLike={handleLike}
          onPass={handlePass}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background.secondary,
  },
  header: {
    paddingHorizontal: Spacing['2xl'],
    paddingTop: Spacing['4xl'],
    paddingBottom: Spacing['3xl'],
    borderBottomLeftRadius: BorderRadius['3xl'],
    borderBottomRightRadius: BorderRadius['3xl'],
  },
  headerContent: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  headerText: {
    flex: 1,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: Spacing.md,
    marginBottom: Spacing.sm,
  },
  headerTitle: {
    fontSize: Typography.fontSize['4xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.inverse,
  },
  headerSubtitle: {
    fontSize: Typography.fontSize.lg,
    color: Colors.primary[100],
    marginBottom: Spacing.md,
    lineHeight: Typography.lineHeight.relaxed * Typography.fontSize.lg,
  },
  locationRow: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
  },
  locationText: {
    fontSize: Typography.fontSize.sm,
    color: Colors.primary[200],
    fontWeight: Typography.fontWeight.medium,
  },
  settingsButton: {
    backgroundColor: Colors.background.primary,
    borderRadius: BorderRadius.xl,
    padding: Spacing.md,
    ...Shadows.md,
  },
  filterSection: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: Spacing['2xl'],
    paddingVertical: Spacing.lg,
    gap: Spacing.lg,
  },
  swipeContainer: {
    flex: 1,
    paddingVertical: Spacing.lg,
  },
  filterTabs: {
    flex: 1,
    flexDirection: 'row',
    gap: Spacing.md,
  },
  filterTab: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.md,
    borderRadius: BorderRadius.full,
    backgroundColor: Colors.background.primary,
    borderWidth: 2,
    borderColor: Colors.border.light,
    gap: Spacing.sm,
    ...Shadows.sm,
  },
  activeFilterTab: {
    backgroundColor: Colors.primary[500],
    borderColor: Colors.primary[500],
  },
  filterEmoji: {
    fontSize: Typography.fontSize.base,
  },
  filterTabText: {
    fontSize: Typography.fontSize.sm,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.secondary,
  },
  activeFilterTabText: {
    color: Colors.text.inverse,
  },
  filterButton: {
    backgroundColor: Colors.background.primary,
    borderRadius: BorderRadius.full,
    padding: Spacing.md,
    borderWidth: 2,
    borderColor: Colors.primary[100],
    ...Shadows.sm,
  },
});