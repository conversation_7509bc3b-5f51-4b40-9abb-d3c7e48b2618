import React, { useState, useRef } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  TouchableOpacity,
  Text,
  Alert,
} from 'react-native';
import * as Haptics from 'expo-haptics';
import {
  PanGestureHandler,
  PanGestureHandlerGestureEvent,
} from 'react-native-gesture-handler';
import Animated, {
  useAnimatedGestureHandler,
  useAnimatedStyle,
  useSharedValue,
  withSpring,
  withTiming,
  runOnJS,
  interpolate,
  Extrapolate,
} from 'react-native-reanimated';
import { Heart, X, RotateCcw, Sparkles } from 'lucide-react-native';
import SwipeCard from './SwipeCard';
import { Colors, Typography, Spacing, BorderRadius, Shadows } from '@/constants/Design';
import { Pet } from '@/types/Pet';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

interface SwipeStackProps {
  pets: Pet[];
  onLike: (pet: Pet) => void;
  onPass: (pet: Pet) => void;
}

const SWIPE_THRESHOLD = screenWidth * 0.3;

export default function SwipeStack({ pets, onLike, onPass }: SwipeStackProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const translateX = useSharedValue(0);
  const translateY = useSharedValue(0);
  const scale = useSharedValue(1);

  const currentPet = pets[currentIndex];
  const nextPet = pets[currentIndex + 1];

  const handleSwipeComplete = (direction: 'left' | 'right') => {
    if (currentPet) {
      // Add haptic feedback
      if (direction === 'right') {
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
        onLike(currentPet);
      } else {
        Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
        onPass(currentPet);
      }
    }

    setCurrentIndex(prev => prev + 1);
    translateX.value = 0;
    translateY.value = 0;
    scale.value = withSpring(1, { damping: 15, stiffness: 150 });
  };

  const gestureHandler = useAnimatedGestureHandler<PanGestureHandlerGestureEvent>({
    onStart: () => {
      scale.value = withSpring(0.95, { damping: 15, stiffness: 200 });
    },
    onActive: (event) => {
      translateX.value = event.translationX;
      translateY.value = event.translationY * 0.5; // Reduce vertical movement
    },
    onEnd: (event) => {
      const shouldSwipeRight = translateX.value > SWIPE_THRESHOLD;
      const shouldSwipeLeft = translateX.value < -SWIPE_THRESHOLD;

      if (shouldSwipeRight) {
        translateX.value = withTiming(screenWidth * 1.5, { duration: 400 });
        translateY.value = withTiming(translateY.value - 100, { duration: 400 });
        runOnJS(handleSwipeComplete)('right');
      } else if (shouldSwipeLeft) {
        translateX.value = withTiming(-screenWidth * 1.5, { duration: 400 });
        translateY.value = withTiming(translateY.value - 100, { duration: 400 });
        runOnJS(handleSwipeComplete)('left');
      } else {
        translateX.value = withSpring(0, { damping: 15, stiffness: 200 });
        translateY.value = withSpring(0, { damping: 15, stiffness: 200 });
        scale.value = withSpring(1, { damping: 15, stiffness: 200 });
      }
    },
  });

  const cardStyle = useAnimatedStyle(() => {
    const rotation = interpolate(
      translateX.value,
      [-screenWidth, 0, screenWidth],
      [-30, 0, 30],
      Extrapolate.CLAMP
    );

    const likeOpacity = interpolate(
      translateX.value,
      [0, SWIPE_THRESHOLD],
      [0, 1],
      Extrapolate.CLAMP
    );

    const passOpacity = interpolate(
      translateX.value,
      [-SWIPE_THRESHOLD, 0],
      [1, 0],
      Extrapolate.CLAMP
    );

    return {
      transform: [
        { translateX: translateX.value },
        { translateY: translateY.value },
        { rotate: `${rotation}deg` },
        { scale: scale.value },
      ],
    };
  });

  const likeIndicatorStyle = useAnimatedStyle(() => ({
    opacity: interpolate(
      translateX.value,
      [0, SWIPE_THRESHOLD],
      [0, 1],
      Extrapolate.CLAMP
    ),
  }));

  const passIndicatorStyle = useAnimatedStyle(() => ({
    opacity: interpolate(
      translateX.value,
      [-SWIPE_THRESHOLD, 0],
      [1, 0],
      Extrapolate.CLAMP
    ),
  }));

  const handleLike = () => {
    scale.value = withSpring(1.05, { damping: 10, stiffness: 200 });
    translateX.value = withTiming(screenWidth * 1.5, { duration: 400 });
    translateY.value = withTiming(-100, { duration: 400 });
    handleSwipeComplete('right');
  };

  const handlePass = () => {
    scale.value = withSpring(1.05, { damping: 10, stiffness: 200 });
    translateX.value = withTiming(-screenWidth * 1.5, { duration: 400 });
    translateY.value = withTiming(-100, { duration: 400 });
    handleSwipeComplete('left');
  };

  const handleRewind = () => {
    if (currentIndex > 0) {
      setCurrentIndex(prev => prev - 1);
    }
  };

  if (currentIndex >= pets.length) {
    return (
      <View style={styles.emptyState}>
        <View style={styles.emptyIcon}>
          <Sparkles size={64} color={Colors.primary[300]} />
        </View>
        <Text style={styles.emptyTitle}>You've seen them all!</Text>
        <Text style={styles.emptySubtitle}>
          Great job exploring! Check back later for new furry friends, or start over to see your favorites again.
        </Text>
        <TouchableOpacity
          style={styles.resetButton}
          onPress={() => setCurrentIndex(0)}
        >
          <RotateCcw size={20} color={Colors.text.inverse} />
          <Text style={styles.resetButtonText}>Start Over</Text>
        </TouchableOpacity>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      <View style={styles.cardStack}>
        {/* Next card (background) */}
        {nextPet && (
          <SwipeCard
            pet={nextPet}
            style={[styles.nextCard]}
          />
        )}

        {/* Current card */}
        {currentPet && (
          <PanGestureHandler onGestureEvent={gestureHandler}>
            <Animated.View style={cardStyle}>
              <SwipeCard pet={currentPet} />

              {/* Animated indicators */}
              <Animated.View style={[styles.likeIndicator, likeIndicatorStyle]}>
                <Heart size={40} color="#10B981" fill="#10B981" />
                <Text style={[styles.indicatorText, { color: '#10B981' }]}>LIKE</Text>
              </Animated.View>

              <Animated.View style={[styles.passIndicator, passIndicatorStyle]}>
                <X size={40} color="#EF4444" />
                <Text style={[styles.indicatorText, { color: '#EF4444' }]}>PASS</Text>
              </Animated.View>
            </Animated.View>
          </PanGestureHandler>
        )}
      </View>

      {/* Action buttons */}
      <View style={styles.actionButtons}>
        <TouchableOpacity
          style={[styles.actionButton, styles.rewindButton]}
          onPress={handleRewind}
          disabled={currentIndex === 0}
        >
          <RotateCcw
            size={20}
            color={currentIndex === 0 ? Colors.neutral[300] : Colors.neutral[600]}
          />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.passButton]}
          onPress={handlePass}
        >
          <X size={24} color={Colors.error} />
        </TouchableOpacity>

        <TouchableOpacity
          style={[styles.actionButton, styles.likeButton]}
          onPress={handleLike}
        >
          <Heart size={24} color={Colors.accent.green[500]} fill={Colors.accent.green[500]} />
        </TouchableOpacity>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
  },
  cardStack: {
    width: screenWidth - Spacing['3xl'],
    height: 620,
    alignItems: 'center',
    justifyContent: 'center',
  },
  nextCard: {
    transform: [{ scale: 0.92 }],
    opacity: 0.6,
  },
  likeIndicator: {
    position: 'absolute',
    top: 120,
    right: Spacing['2xl'],
    alignItems: 'center',
    transform: [{ rotate: '-15deg' }],
  },
  passIndicator: {
    position: 'absolute',
    top: 120,
    left: Spacing['2xl'],
    alignItems: 'center',
    transform: [{ rotate: '15deg' }],
  },
  indicatorText: {
    fontSize: Typography.fontSize.lg,
    fontWeight: Typography.fontWeight.extrabold,
    marginTop: 4,
    letterSpacing: 1,
  },
  actionButtons: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: Spacing['4xl'],
    gap: Spacing['2xl'],
    paddingHorizontal: Spacing['2xl'],
  },
  actionButton: {
    width: 64,
    height: 64,
    borderRadius: BorderRadius.full,
    alignItems: 'center',
    justifyContent: 'center',
    ...Shadows.lg,
  },
  rewindButton: {
    backgroundColor: Colors.background.primary,
    width: 48,
    height: 48,
    borderWidth: 2,
    borderColor: Colors.border.medium,
  },
  passButton: {
    backgroundColor: Colors.background.primary,
    borderWidth: 3,
    borderColor: Colors.error,
  },
  likeButton: {
    backgroundColor: Colors.accent.green[500],
    borderWidth: 3,
    borderColor: Colors.accent.green[600],
  },
  emptyState: {
    flex: 1,
    alignItems: 'center',
    justifyContent: 'center',
    paddingHorizontal: Spacing['4xl'],
  },
  emptyIcon: {
    marginBottom: Spacing['2xl'],
  },
  emptyTitle: {
    fontSize: Typography.fontSize['3xl'],
    fontWeight: Typography.fontWeight.bold,
    color: Colors.text.primary,
    marginBottom: Spacing.lg,
    textAlign: 'center',
  },
  emptySubtitle: {
    fontSize: Typography.fontSize.base,
    color: Colors.text.secondary,
    textAlign: 'center',
    lineHeight: Typography.lineHeight.relaxed * Typography.fontSize.base,
    marginBottom: Spacing['4xl'],
  },
  resetButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary[500],
    borderRadius: BorderRadius.full,
    paddingHorizontal: Spacing['2xl'],
    paddingVertical: Spacing.lg,
    gap: Spacing.sm,
    ...Shadows.md,
  },
  resetButtonText: {
    fontSize: Typography.fontSize.base,
    fontWeight: Typography.fontWeight.semibold,
    color: Colors.text.inverse,
  },
});